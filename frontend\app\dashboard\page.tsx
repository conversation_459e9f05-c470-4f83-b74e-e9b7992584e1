export default function DashboardPage() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f9fafb',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        padding: '24px'
      }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#111827',
          textAlign: 'center'
        }}>
          لوحة التحكم
        </h1>
      </header>

      {/* Main Content */}
      <main style={{ padding: '24px' }}>
        {/* Welcome Card */}
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          marginBottom: '32px',
          textAlign: 'center'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#111827',
            marginBottom: '16px'
          }}>
            مرحباً بك في نظام إدارة الشحنات
          </h2>
          <p style={{
            color: '#6b7280',
            marginBottom: '24px'
          }}>
            نظام شامل لإدارة الشحنات والتوصيل
          </p>

          {/* Quick Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
            marginTop: '24px'
          }}>
            <div style={{
              backgroundColor: '#dbeafe',
              padding: '24px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2563eb' }}>0</div>
              <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>إجمالي الشحنات</div>
            </div>
            <div style={{
              backgroundColor: '#fef3c7',
              padding: '24px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#d97706' }}>0</div>
              <div style={{ fontSize: '0.875rem', color: '#92400e' }}>الشحنات المعلقة</div>
            </div>
            <div style={{
              backgroundColor: '#dcfce7',
              padding: '24px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#16a34a' }}>0</div>
              <div style={{ fontSize: '0.875rem', color: '#15803d' }}>الشحنات المسلمة</div>
            </div>
            <div style={{
              backgroundColor: '#f3e8ff',
              padding: '24px',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#9333ea' }}>$0</div>
              <div style={{ fontSize: '0.875rem', color: '#7c3aed' }}>إجمالي الإيرادات</div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        }}>
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '16px'
          }}>
            حالة النظام
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{
                width: '12px',
                height: '12px',
                backgroundColor: '#10b981',
                borderRadius: '50%'
              }}></div>
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                النظام يعمل بشكل طبيعي
              </span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{
                width: '12px',
                height: '12px',
                backgroundColor: '#10b981',
                borderRadius: '50%'
              }}></div>
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                قاعدة البيانات متصلة
              </span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{
                width: '12px',
                height: '12px',
                backgroundColor: '#10b981',
                borderRadius: '50%'
              }}></div>
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                API يعمل
              </span>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
