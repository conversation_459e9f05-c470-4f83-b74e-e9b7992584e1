// Application Configuration
// تكوين التطبيق

import dotenv from 'dotenv'
import { z } from 'zod'

// Load environment variables
dotenv.config()

// Environment variables schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  HOST: z.string().default('localhost'),
  
  // Database
  DATABASE_URL: z.string().min(1, 'Database URL is required'),
  
  // JWT
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  
  // CORS
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  
  // Email
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  FROM_EMAIL: z.string().optional(),
  FROM_NAME: z.string().optional(),
  
  // Currency Exchange API
  EXCHANGE_API_KEY: z.string().optional(),
  EXCHANGE_API_URL: z.string().default('https://api.exchangerate-api.com/v4/latest'),
  
  // File Upload
  UPLOAD_MAX_SIZE: z.string().default('10MB'),
  ALLOWED_FILE_TYPES: z.string().default('jpg,jpeg,png,pdf,doc,docx'),
  UPLOAD_PATH: z.string().default('./uploads'),
  
  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW: z.string().default('15m'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Supabase (optional)
  SUPABASE_URL: z.string().optional(),
  SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
})

// Validate environment variables
const env = envSchema.parse(process.env)

// Application configuration
export const config = {
  // Server
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  host: env.HOST,
  
  // Database
  databaseUrl: env.DATABASE_URL,
  
  // JWT
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
  },
  
  // CORS
  corsOrigin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
  
  // Email
  email: {
    host: env.SMTP_HOST,
    port: env.SMTP_PORT,
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
    from: {
      email: env.FROM_EMAIL,
      name: env.FROM_NAME,
    },
  },
  
  // Currency Exchange
  exchange: {
    apiKey: env.EXCHANGE_API_KEY,
    apiUrl: env.EXCHANGE_API_URL,
  },
  
  // File Upload
  upload: {
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.ALLOWED_FILE_TYPES.split(',').map(type => type.trim()),
    path: env.UPLOAD_PATH,
  },
  
  // Security
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
    rateLimit: {
      window: env.RATE_LIMIT_WINDOW,
      maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    },
  },
  
  // Logging
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
  },
  
  // Supabase
  supabase: {
    url: env.SUPABASE_URL,
    anonKey: env.SUPABASE_ANON_KEY,
    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,
  },
  
  // Application constants
  constants: {
    // Default pagination
    defaultPageSize: 20,
    maxPageSize: 100,
    
    // Shipment tracking
    trackingNumberLength: 10,
    trackingNumberPrefix: 'SHP',
    
    // Currency update interval (in minutes)
    currencyUpdateInterval: 60,
    
    // Notification settings
    maxNotificationsPerUser: 100,
    notificationRetentionDays: 30,
    
    // File upload limits
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],
    allowedDocumentTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    
    // Validation rules
    minPasswordLength: 8,
    maxNameLength: 100,
    maxAddressLength: 500,
    maxNotesLength: 1000,
    
    // Business rules
    maxShipmentWeight: 1000, // kg
    maxShipmentDimension: 300, // cm
    defaultEstimatedDeliveryDays: 3,
    
    // API versioning
    apiVersion: 'v1',
    
    // Cache TTL (in seconds)
    cacheTTL: {
      currencies: 3600, // 1 hour
      reports: 300, // 5 minutes
      userProfile: 1800, // 30 minutes
    },
  },
} as const

// Type for configuration
export type Config = typeof config

// Validate required configuration
export function validateConfig(): void {
  const requiredFields = [
    'databaseUrl',
    'jwt.secret',
  ]
  
  for (const field of requiredFields) {
    const value = field.split('.').reduce((obj, key) => obj?.[key], config as any)
    if (!value) {
      throw new Error(`Missing required configuration: ${field}`)
    }
  }
}

// Initialize configuration validation
validateConfig()
