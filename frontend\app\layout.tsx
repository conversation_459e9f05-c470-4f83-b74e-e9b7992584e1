import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
// import './globals.css'
import { Providers } from './providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'نظام إدارة الشحنات | Shipment Management System',
    template: '%s | نظام إدارة الشحنات',
  },
  description: 'نظام متكامل لإدارة الشحنات والتوصيل مع دعم اللغتين العربية والإنجليزية',
  keywords: ['shipment', 'delivery', 'management', 'logistics', 'tracking', 'شحنات', 'توصيل', 'إدارة'],
  authors: [{ name: 'عصام' }],
  creator: 'عصام',
  publisher: 'Shipment Management System',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'ar': '/ar',
      'en': '/en',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'ar_SA',
    url: 'http://localhost:3000',
    title: 'نظام إدارة الشحنات | Shipment Management System',
    description: 'نظام متكامل لإدارة الشحنات والتوصيل مع دعم اللغتين العربية والإنجليزية',
    siteName: 'Shipment Management System',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'نظام إدارة الشحنات | Shipment Management System',
    description: 'نظام متكامل لإدارة الشحنات والتوصيل مع دعم اللغتين العربية والإنجليزية',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#007AFF" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="نظام إدارة الشحنات" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#007AFF" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <Providers>
          {children}
          <Toaster
            position="top-center"
            reverseOrder={false}
            gutter={8}
            containerClassName=""
            containerStyle={{}}
            toastOptions={{
              // Default options for all toasts
              className: '',
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
                fontFamily: 'SF Pro Text, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
              },
              // Default options for specific types
              success: {
                duration: 3000,
                style: {
                  background: '#34C759',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#34C759',
                },
              },
              error: {
                duration: 5000,
                style: {
                  background: '#FF3B30',
                },
                iconTheme: {
                  primary: '#fff',
                  secondary: '#FF3B30',
                },
              },
              loading: {
                duration: Infinity,
                style: {
                  background: '#007AFF',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
